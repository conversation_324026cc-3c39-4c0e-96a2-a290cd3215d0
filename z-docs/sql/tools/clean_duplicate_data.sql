-- 清理数据库重复数据脚本

-- ========================================
-- 备份当前数据（可选）
-- ========================================

-- 如果需要备份，可以取消注释以下语句
-- CREATE TABLE t_sys_entitlement_backup AS SELECT * FROM t_sys_entitlement;
-- CREATE TABLE t_sys_role_ent_rela_backup AS SELECT * FROM t_sys_role_ent_rela;

-- ========================================
-- 1. 清理权限表重复数据
-- ========================================

SELECT '=== 开始清理权限表重复数据 ===' as cleanup_info;

-- 删除重复的权限记录，保留最早创建的记录
DELETE e1 FROM t_sys_entitlement e1
INNER JOIN t_sys_entitlement e2 
WHERE e1.ent_id = e2.ent_id 
AND e1.sys_type = e2.sys_type
AND e1.created_at > e2.created_at;

-- 统计清理后的权限数量
SELECT '=== 清理后权限统计 ===' as cleanup_info;
SELECT sys_type, COUNT(*) as permission_count 
FROM t_sys_entitlement 
GROUP BY sys_type 
ORDER BY permission_count DESC;

-- ========================================
-- 2. 清理角色权限关联表重复数据
-- ========================================

SELECT '=== 开始清理角色权限关联表重复数据 ===' as cleanup_info;

-- 删除重复的角色权限关联记录
-- 由于这个表没有时间戳，我们使用DISTINCT来重建
CREATE TEMPORARY TABLE temp_role_ent_rela AS 
SELECT DISTINCT role_id, ent_id 
FROM t_sys_role_ent_rela;

-- 清空原表
DELETE FROM t_sys_role_ent_rela;

-- 插入去重后的数据
INSERT INTO t_sys_role_ent_rela (role_id, ent_id)
SELECT role_id, ent_id FROM temp_role_ent_rela;

-- 删除临时表
DROP TEMPORARY TABLE temp_role_ent_rela;

-- ========================================
-- 3. 清理用户角色关联表重复数据
-- ========================================

SELECT '=== 开始清理用户角色关联表重复数据 ===' as cleanup_info;

-- 删除重复的用户角色关联记录
CREATE TEMPORARY TABLE temp_user_role_rela AS 
SELECT DISTINCT user_id, role_id 
FROM t_sys_user_role_rela;

-- 清空原表
DELETE FROM t_sys_user_role_rela;

-- 插入去重后的数据
INSERT INTO t_sys_user_role_rela (user_id, role_id)
SELECT user_id, role_id FROM temp_user_role_rela;

-- 删除临时表
DROP TEMPORARY TABLE temp_user_role_rela;

-- ========================================
-- 4. 清理孤立权限（没有分配给任何角色的权限）
-- ========================================

SELECT '=== 清理孤立权限 ===' as cleanup_info;

-- 显示将要删除的孤立权限
SELECT '--- 将要删除的孤立权限 ---' as cleanup_info;
SELECT e.ent_id, e.ent_name, e.sys_type
FROM t_sys_entitlement e
LEFT JOIN t_sys_role_ent_rela r ON e.ent_id = r.ent_id
WHERE r.ent_id IS NULL
AND e.ent_type = 'PB'  -- 只删除按钮权限，保留菜单权限
ORDER BY e.sys_type, e.ent_id;

-- 删除孤立的按钮权限（保留菜单权限，因为菜单权限可能用于显示）
DELETE e FROM t_sys_entitlement e
LEFT JOIN t_sys_role_ent_rela r ON e.ent_id = r.ent_id
WHERE r.ent_id IS NULL
AND e.ent_type = 'PB';

-- ========================================
-- 5. 验证清理结果
-- ========================================

SELECT '=== 清理完成，验证结果 ===' as cleanup_info;

-- 检查是否还有重复记录
SELECT '--- 权限表重复检查 ---' as cleanup_info;
SELECT ent_id, COUNT(*) as count 
FROM t_sys_entitlement 
GROUP BY ent_id 
HAVING COUNT(*) > 1;

SELECT '--- 角色权限关联重复检查 ---' as cleanup_info;
SELECT role_id, ent_id, COUNT(*) as count 
FROM t_sys_role_ent_rela 
GROUP BY role_id, ent_id 
HAVING COUNT(*) > 1;

SELECT '--- 用户角色关联重复检查 ---' as cleanup_info;
SELECT user_id, role_id, COUNT(*) as count 
FROM t_sys_user_role_rela 
GROUP BY user_id, role_id 
HAVING COUNT(*) > 1;

-- 最终统计
SELECT '--- 最终统计 ---' as cleanup_info;
SELECT 't_sys_entitlement' as table_name, COUNT(*) as total_records FROM t_sys_entitlement
UNION ALL
SELECT 't_sys_role_ent_rela' as table_name, COUNT(*) as total_records FROM t_sys_role_ent_rela
UNION ALL
SELECT 't_sys_role' as table_name, COUNT(*) as total_records FROM t_sys_role
UNION ALL
SELECT 't_sys_user_role_rela' as table_name, COUNT(*) as total_records FROM t_sys_user_role_rela;

COMMIT;
