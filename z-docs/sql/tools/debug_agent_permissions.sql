-- 代理商权限问题排查脚本
-- 请使用您的数据库连接信息执行此脚本

-- 1. 检查数据库连接和基本信息
SELECT '=== 数据库基本信息 ===' as info;
SELECT DATABASE() as current_database, USER() as current_user, NOW() as current_time;

-- 2. 检查权限表是否存在
SELECT '=== 检查核心表是否存在 ===' as info;
SELECT TABLE_NAME, TABLE_ROWS 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('t_sys_entitlement', 't_sys_role', 't_sys_role_ent_rela', 't_sys_user', 't_sys_user_role_rela')
ORDER BY TABLE_NAME;

-- 3. 检查代理商权限是否已插入
SELECT '=== 检查代理商权限记录 ===' as info;
SELECT COUNT(*) as agent_permission_count
FROM t_sys_entitlement 
WHERE ent_id LIKE 'ENT_AGENT%';

SELECT ent_id, ent_name, ent_type, component, parent_id, sort_no, state
FROM t_sys_entitlement 
WHERE ent_id LIKE 'ENT_AGENT%' 
ORDER BY parent_id, sort_no;

-- 4. 检查所有管理系统的权限结构
SELECT '=== 管理系统权限结构 ===' as info;
SELECT ent_id, ent_name, ent_type, component, parent_id, sort_no, state
FROM t_sys_entitlement 
WHERE sys_type = 'MGR' AND parent_id = 'ROOT'
ORDER BY sort_no;

-- 5. 检查系统中的角色
SELECT '=== 系统角色信息 ===' as info;
SELECT role_id, role_name, sys_type, state, created_at
FROM t_sys_role 
WHERE sys_type = 'MGR'
ORDER BY created_at;

-- 6. 检查用户信息
SELECT '=== 用户信息 ===' as info;
SELECT sys_user_id, username, realname, state, sys_type, created_at
FROM t_sys_user 
WHERE sys_type = 'MGR'
ORDER BY created_at;

-- 7. 检查用户角色关联
SELECT '=== 用户角色关联 ===' as info;
SELECT u.username, u.realname, r.role_name, ur.created_at
FROM t_sys_user u
JOIN t_sys_user_role_rela ur ON u.sys_user_id = ur.user_id
JOIN t_sys_role r ON ur.role_id = r.role_id
WHERE u.sys_type = 'MGR'
ORDER BY u.username;

-- 8. 检查角色权限关联（重点检查代理商权限）
SELECT '=== 角色权限关联（代理商相关） ===' as info;
SELECT r.role_name, e.ent_id, e.ent_name
FROM t_sys_role_ent_rela re
JOIN t_sys_role r ON re.role_id = r.role_id
JOIN t_sys_entitlement e ON re.ent_id = e.ent_id
WHERE e.ent_id LIKE 'ENT_AGENT%'
ORDER BY r.role_name, e.ent_id;

-- 9. 检查当前登录用户应该有的权限（假设用户名为admin）
SELECT '=== 检查admin用户的所有权限 ===' as info;
SELECT DISTINCT e.ent_id, e.ent_name, e.ent_type, e.component
FROM t_sys_user u
JOIN t_sys_user_role_rela ur ON u.sys_user_id = ur.user_id
JOIN t_sys_role_ent_rela re ON ur.role_id = re.role_id
JOIN t_sys_entitlement e ON re.ent_id = e.ent_id
WHERE u.username = 'admin' AND e.sys_type = 'MGR' AND e.state = 1
ORDER BY e.parent_id, e.sort_no;

-- 10. 如果代理商权限不存在，显示插入语句
SELECT '=== 如果需要插入代理商权限，请执行以下语句 ===' as info;

-- 检查是否需要插入权限
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '需要执行插入语句'
        ELSE '权限已存在，无需插入'
    END as status
FROM t_sys_entitlement 
WHERE ent_id = 'ENT_AGENT';
