-- 精确清理完全重复的数据

-- ========================================
-- 1. 清理权限表中完全重复的记录
-- ========================================

SELECT '=== 清理权限表完全重复记录 ===' as cleanup_info;

-- 查找完全重复的记录（所有字段都相同）
SELECT '--- 完全重复的权限记录 ---' as cleanup_info;
SELECT ent_id, ent_name, ent_type, pid, sys_type, COUNT(*) as count
FROM t_sys_entitlement 
GROUP BY ent_id, ent_name, ent_type, pid, sys_type, menu_icon, menu_uri, component_name, ent_sort, quick_jump, state
HAVING COUNT(*) > 1
ORDER BY count DESC;

-- 删除完全重复的记录，保留一条
DELETE e1 FROM t_sys_entitlement e1
INNER JOIN t_sys_entitlement e2 
WHERE e1.ent_id = e2.ent_id 
AND e1.ent_name = e2.ent_name
AND e1.ent_type = e2.ent_type
AND e1.pid = e2.pid
AND e1.sys_type = e2.sys_type
AND e1.menu_icon = e2.menu_icon
AND e1.menu_uri = e2.menu_uri
AND e1.component_name = e2.component_name
AND e1.ent_sort = e2.ent_sort
AND e1.quick_jump = e2.quick_jump
AND e1.state = e2.state
AND e1.created_at > e2.created_at;

-- ========================================
-- 2. 重新构建代理商系统权限（确保一致性）
-- ========================================

SELECT '=== 重新构建代理商系统权限 ===' as cleanup_info;

-- 删除所有代理商系统权限
DELETE FROM t_sys_entitlement WHERE sys_type = 'AGENT';

-- 重新插入代理商系统权限（基于管理系统权限，但适配代理商系统）
INSERT INTO t_sys_entitlement (ent_id, ent_name, menu_icon, menu_uri, component_name, ent_type, quick_jump, state, pid, ent_sort, sys_type, created_at, updated_at) VALUES
-- 通用菜单
('ENT_COMMONS', '系统通用菜单', 'no-icon', '', 'RouteView', 'MO', 0, 1, 'ROOT', '-1', 'AGENT', NOW(), NOW()),
('ENT_C_USERINFO', '个人中心', 'no-icon', '/current/userinfo', 'CurrentUserInfo', 'MO', 0, 1, 'ENT_COMMONS', '-1', 'AGENT', NOW(), NOW()),

-- 主页
('ENT_C_MAIN', '主页', 'home', '/main', 'MainPage', 'ML', 0, 1, 'ROOT', '1', 'AGENT', NOW(), NOW()),
('ENT_C_MAIN_PAY_AMOUNT_WEEK', '主页周支付统计', 'no-icon', '', '', 'PB', 0, 1, 'ENT_C_MAIN', '0', 'AGENT', NOW(), NOW()),
('ENT_C_MAIN_NUMBER_COUNT', '主页数量总统计', 'no-icon', '', '', 'PB', 0, 1, 'ENT_C_MAIN', '0', 'AGENT', NOW(), NOW()),
('ENT_C_MAIN_PAY_COUNT', '主页交易统计', 'no-icon', '', '', 'PB', 0, 1, 'ENT_C_MAIN', '0', 'AGENT', NOW(), NOW()),
('ENT_C_MAIN_PAY_TYPE_COUNT', '主页交易方式统计', 'no-icon', '', '', 'PB', 0, 1, 'ENT_C_MAIN', '0', 'AGENT', NOW(), NOW()),

-- 商户管理
('ENT_MCH', '商户管理', 'shop', '', 'RouteView', 'ML', 0, 1, 'ROOT', '30', 'AGENT', NOW(), NOW()),
('ENT_MCH_INFO', '商户列表', 'profile', '/mch', 'MchListPage', 'ML', 0, 1, 'ENT_MCH', '10', 'AGENT', NOW(), NOW()),
('ENT_MCH_LIST', '页面：商户列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_INFO', '0', 'AGENT', NOW(), NOW()),
('ENT_MCH_INFO_ADD', '按钮：新增', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_INFO', '0', 'AGENT', NOW(), NOW()),
('ENT_MCH_INFO_EDIT', '按钮：编辑', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_INFO', '0', 'AGENT', NOW(), NOW()),
('ENT_MCH_INFO_VIEW', '按钮：详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_MCH_INFO', '0', 'AGENT', NOW(), NOW()),

-- 代理商管理
('ENT_AGENT', '代理商管理', 'team', '', 'RouteView', 'ML', 0, 1, 'ROOT', '20', 'AGENT', NOW(), NOW()),
('ENT_AGENT_INFO', '下级代理商', 'user', '/agent', 'AgentListPage', 'ML', 0, 1, 'ENT_AGENT', '10', 'AGENT', NOW(), NOW()),
('ENT_AGENT_LIST', '页面：代理商列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_AGENT_INFO', '0', 'AGENT', NOW(), NOW()),
('ENT_AGENT_INFO_ADD', '按钮：新增', 'no-icon', '', '', 'PB', 0, 1, 'ENT_AGENT_INFO', '0', 'AGENT', NOW(), NOW()),
('ENT_AGENT_INFO_EDIT', '按钮：编辑', 'no-icon', '', '', 'PB', 0, 1, 'ENT_AGENT_INFO', '0', 'AGENT', NOW(), NOW()),
('ENT_AGENT_INFO_VIEW', '按钮：详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_AGENT_INFO', '0', 'AGENT', NOW(), NOW()),
('ENT_AGENT_INFO_DELETE', '按钮：删除', 'no-icon', '', '', 'PB', 0, 1, 'ENT_AGENT_INFO', '0', 'AGENT', NOW(), NOW()),

-- 订单管理
('ENT_ORDER', '订单管理', 'account-book', '', 'RouteView', 'ML', 0, 1, 'ROOT', '40', 'AGENT', NOW(), NOW()),
('ENT_PAY_ORDER', '支付订单', 'file-text', '/payOrder', 'PayOrderListPage', 'ML', 0, 1, 'ENT_ORDER', '10', 'AGENT', NOW(), NOW()),
('ENT_ORDER_LIST', '页面：订单列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_PAY_ORDER', '0', 'AGENT', NOW(), NOW()),
('ENT_PAY_ORDER_VIEW', '按钮：详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_PAY_ORDER', '0', 'AGENT', NOW(), NOW()),
('ENT_PAY_ORDER_REFUND', '按钮：发起退款', 'no-icon', '', '', 'PB', 0, 1, 'ENT_PAY_ORDER', '0', 'AGENT', NOW(), NOW()),
('ENT_REFUND_ORDER', '退款订单', 'file-text', '/refundOrder', 'RefundOrderListPage', 'ML', 0, 1, 'ENT_ORDER', '20', 'AGENT', NOW(), NOW()),
('ENT_REFUND_LIST', '页面：退款订单列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_REFUND_ORDER', '0', 'AGENT', NOW(), NOW()),
('ENT_REFUND_ORDER_VIEW', '按钮：详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_REFUND_ORDER', '0', 'AGENT', NOW(), NOW()),

-- 支付配置
('ENT_PC', '支付配置', 'setting', '', 'RouteView', 'ML', 0, 1, 'ROOT', '50', 'AGENT', NOW(), NOW()),
('ENT_PC_WAY', '支付方式', 'credit-card', '/payways', 'PayWayPage', 'ML', 0, 1, 'ENT_PC', '10', 'AGENT', NOW(), NOW()),
('ENT_PC_WAY_LIST', '页面：支付方式列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_PC_WAY', '0', 'AGENT', NOW(), NOW()),
('ENT_PC_WAY_VIEW', '按钮：详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_PC_WAY', '0', 'AGENT', NOW(), NOW()),

-- 系统管理
('ENT_SYS_CONFIG', '系统管理', 'setting', '', 'RouteView', 'ML', 0, 1, 'ROOT', '200', 'AGENT', NOW(), NOW()),
('ENT_UR', '用户角色管理', 'team', '', 'RouteView', 'ML', 0, 1, 'ENT_SYS_CONFIG', '10', 'AGENT', NOW(), NOW()),
('ENT_UR_USER', '操作员管理', 'contacts', '/users', 'SysUserPage', 'ML', 0, 1, 'ENT_UR', '10', 'AGENT', NOW(), NOW()),
('ENT_UR_USER_LIST', '页面：操作员列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_USER', '0', 'AGENT', NOW(), NOW()),
('ENT_UR_USER_ADD', '按钮：新增操作员', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_USER', '0', 'AGENT', NOW(), NOW()),
('ENT_UR_USER_EDIT', '按钮：编辑操作员', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_USER', '0', 'AGENT', NOW(), NOW()),
('ENT_UR_USER_VIEW', '按钮：详情', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_USER', '0', 'AGENT', NOW(), NOW()),
('ENT_UR_ROLE', '角色管理', 'solution', '/roles', 'RolePage', 'ML', 0, 1, 'ENT_UR', '20', 'AGENT', NOW(), NOW()),
('ENT_UR_ROLE_LIST', '页面：角色列表', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_ROLE', '0', 'AGENT', NOW(), NOW()),
('ENT_UR_ROLE_ADD', '按钮：新增角色', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_ROLE', '0', 'AGENT', NOW(), NOW()),
('ENT_UR_ROLE_EDIT', '按钮：编辑角色', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_ROLE', '0', 'AGENT', NOW(), NOW()),
('ENT_UR_ROLE_DIST', '按钮：分配权限', 'no-icon', '', '', 'PB', 0, 1, 'ENT_UR_ROLE', '0', 'AGENT', NOW(), NOW());

-- ========================================
-- 3. 重新分配代理商管理员权限
-- ========================================

SELECT '=== 重新分配代理商管理员权限 ===' as cleanup_info;

-- 删除旧的代理商管理员权限
DELETE FROM t_sys_role_ent_rela WHERE role_id = 'ROLE_AGENT_ADMIN';

-- 重新分配权限
INSERT INTO t_sys_role_ent_rela (role_id, ent_id) 
SELECT 'ROLE_AGENT_ADMIN', ent_id 
FROM t_sys_entitlement 
WHERE sys_type = 'AGENT';

-- ========================================
-- 4. 最终验证
-- ========================================

SELECT '=== 清理完成验证 ===' as cleanup_info;

SELECT '--- 各系统权限统计 ---' as cleanup_info;
SELECT sys_type, COUNT(*) as permission_count 
FROM t_sys_entitlement 
GROUP BY sys_type 
ORDER BY permission_count DESC;

SELECT '--- 代理商管理员权限数量 ---' as cleanup_info;
SELECT COUNT(*) as agent_admin_permissions 
FROM t_sys_role_ent_rela 
WHERE role_id = 'ROLE_AGENT_ADMIN';

COMMIT;
