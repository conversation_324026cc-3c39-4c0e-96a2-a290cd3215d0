-- 分析和清理数据库重复数据

-- ========================================
-- 1. 分析权限表重复数据
-- ========================================

-- 检查 t_sys_entitlement 表中的重复记录
SELECT '=== 权限表重复数据分析 ===' as analysis_info;

-- 按 ent_id 分组查找重复
SELECT ent_id, COUNT(*) as count 
FROM t_sys_entitlement 
GROUP BY ent_id 
HAVING COUNT(*) > 1 
ORDER BY count DESC;

-- 显示具体的重复记录
SELECT ent_id, ent_name, ent_type, pid, sys_type, created_at
FROM t_sys_entitlement 
WHERE ent_id IN (
    SELECT ent_id 
    FROM t_sys_entitlement 
    GROUP BY ent_id 
    HAVING COUNT(*) > 1
)
ORDER BY ent_id, created_at;

-- ========================================
-- 2. 分析角色权限关联表重复数据
-- ========================================

SELECT '=== 角色权限关联表重复数据分析 ===' as analysis_info;

-- 按 role_id, ent_id 分组查找重复
SELECT role_id, ent_id, COUNT(*) as count 
FROM t_sys_role_ent_rela 
GROUP BY role_id, ent_id 
HAVING COUNT(*) > 1 
ORDER BY count DESC;

-- ========================================
-- 3. 分析用户角色关联表重复数据
-- ========================================

SELECT '=== 用户角色关联表重复数据分析 ===' as analysis_info;

-- 按 user_id, role_id 分组查找重复
SELECT user_id, role_id, COUNT(*) as count 
FROM t_sys_user_role_rela 
GROUP BY user_id, role_id 
HAVING COUNT(*) > 1 
ORDER BY count DESC;

-- ========================================
-- 4. 统计各系统类型的权限数量
-- ========================================

SELECT '=== 各系统类型权限统计 ===' as analysis_info;

SELECT sys_type, COUNT(*) as permission_count 
FROM t_sys_entitlement 
GROUP BY sys_type 
ORDER BY permission_count DESC;

-- ========================================
-- 5. 检查孤立的权限（没有分配给任何角色的权限）
-- ========================================

SELECT '=== 孤立权限检查 ===' as analysis_info;

SELECT e.ent_id, e.ent_name, e.sys_type
FROM t_sys_entitlement e
LEFT JOIN t_sys_role_ent_rela r ON e.ent_id = r.ent_id
WHERE r.ent_id IS NULL
ORDER BY e.sys_type, e.ent_id;

-- ========================================
-- 6. 检查无效的权限关联（权限不存在但有角色关联）
-- ========================================

SELECT '=== 无效权限关联检查 ===' as analysis_info;

SELECT r.role_id, r.ent_id
FROM t_sys_role_ent_rela r
LEFT JOIN t_sys_entitlement e ON r.ent_id = e.ent_id
WHERE e.ent_id IS NULL
ORDER BY r.role_id, r.ent_id;
