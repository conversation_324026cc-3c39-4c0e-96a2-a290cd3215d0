package com.unipay.agent.ctrl.order;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.unipay.components.mq.model.PayOrderMchNotifyMQ;
import com.unipay.components.mq.vender.IMQSender;
import com.unipay.core.constants.ApiCodeEnum;
import com.unipay.core.entity.MchNotifyRecord;
import com.unipay.core.exception.BizException;
import com.unipay.core.model.ApiPageRes;
import com.unipay.core.model.ApiRes;
import com.unipay.agent.ctrl.CommonCtrl;
import com.unipay.service.impl.MchNotifyRecordService;
import com.unipay.service.impl.AgentMchRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商户通知类
 *
 * <AUTHOR> 
 * @date 2024-01-01
 */
@Tag(name = "订单管理（商户通知）")
@RestController
@RequestMapping("/api/mchNotify")
public class MchNotifyController extends CommonCtrl {

    @Autowired private MchNotifyRecordService mchNotifyService;
    @Autowired private IMQSender mqSender;
    @Autowired private AgentMchRelationService agentMchRelationService;

    /**
     * @author: terrfly
     * @date: 2024/1/1 16:14
     * @describe: 商户通知信息列表
     */
    @Operation(summary = "商户通知信息列表")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "pageNumber", description = "分页页码"),
            @Parameter(name = "pageSize", description = "分页条数"),
            @Parameter(name = "createdStart", description = "日期格式字符串（yyyy-MM-dd HH:mm:ss），时间范围查询--开始时间，查询范围：大于等于此时间"),
            @Parameter(name = "createdEnd", description = "日期格式字符串（yyyy-MM-dd HH:mm:ss），时间范围查询--结束时间，查询范围：小于等于此时间"),
            @Parameter(name = "mchNo", description = "商户号"),
            @Parameter(name = "appId", description = "应用ID"),
            @Parameter(name = "state", description = "通知状态,1-通知中,2-通知成功,3-通知失败"),
            @Parameter(name = "orderType", description = "订单类型:1-支付,2-退款")
    })
    @PreAuthorize("hasAuthority('ENT_NOTIFY_LIST')")
    @GetMapping
    public ApiPageRes<MchNotifyRecord> list() {

        MchNotifyRecord mchNotify = getObject(MchNotifyRecord.class);
        JSONObject paramJSON = getReqParamJSON();
        
        // 获取当前代理商关联的所有商户号
        String currentAgentNo = getCurrentUser().getSysUser().getBelongInfoId();
        List<String> mchNos = agentMchRelationService.getMchNosByAgentNo(currentAgentNo);
        
        LambdaQueryWrapper<MchNotifyRecord> wrapper = MchNotifyRecord.gw();
        
        // 限制只能查看代理商关联的商户通知记录
        if (mchNos.isEmpty()) {
            // 如果没有关联商户，返回空结果
            wrapper.eq(MchNotifyRecord::getMchNo, "NONE");
        } else {
            wrapper.in(MchNotifyRecord::getMchNo, mchNos);
        }
        
        if (StringUtils.isNotEmpty(mchNotify.getOrderId())) {
            wrapper.eq(MchNotifyRecord::getOrderId, mchNotify.getOrderId());
        }
        if (StringUtils.isNotEmpty(mchNotify.getMchNo())) {
            wrapper.eq(MchNotifyRecord::getMchNo, mchNotify.getMchNo());
        }
        if (StringUtils.isNotEmpty(mchNotify.getAppId())) {
            wrapper.eq(MchNotifyRecord::getAppId, mchNotify.getAppId());
        }
        if (mchNotify.getState() != null) {
            wrapper.eq(MchNotifyRecord::getState, mchNotify.getState());
        }
        if (mchNotify.getOrderType() != null) {
            wrapper.eq(MchNotifyRecord::getOrderType, mchNotify.getOrderType());
        }
        
        if (StringUtils.isNotEmpty(paramJSON.getString("createdStart"))) {
            wrapper.ge(MchNotifyRecord::getCreatedAt, paramJSON.getString("createdStart"));
        }
        if (StringUtils.isNotEmpty(paramJSON.getString("createdEnd"))) {
            wrapper.le(MchNotifyRecord::getCreatedAt, paramJSON.getString("createdEnd"));
        }
        
        wrapper.orderByDesc(MchNotifyRecord::getCreatedAt);
        IPage<MchNotifyRecord> pages = mchNotifyService.page(getIPage(), wrapper);
        return ApiPageRes.pages(pages);
    }

    /**
     * @author: terrfly
     * @date: 2024/1/1 16:14
     * @describe: 商户通知信息详情
     */
    @Operation(summary = "通知信息详情")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "notifyId", description = "商户通知记录ID", required = true)
    })
    @PreAuthorize("hasAuthority('ENT_MCH_NOTIFY_VIEW')")
    @GetMapping("/{notifyId}")
    public ApiRes<MchNotifyRecord> detail(@PathVariable("notifyId") String notifyId) {
        MchNotifyRecord mchNotify = mchNotifyService.getById(notifyId);
        if (mchNotify == null) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_SELETE);
        }
        
        // 检查是否有权限查看该通知记录（记录必须属于代理商关联的商户）
        String currentAgentNo = getCurrentUser().getSysUser().getBelongInfoId();
        List<String> mchNos = agentMchRelationService.getMchNosByAgentNo(currentAgentNo);
        
        if (!mchNos.contains(mchNotify.getMchNo())) {
            return ApiRes.fail(ApiCodeEnum.SYS_PERMISSION_ERROR);
        }
        
        return ApiRes.ok(mchNotify);
    }

    /**
     * @author: terrfly
     * @date: 2024/1/1 16:14
     * @describe: 重发通知
     */
    @Operation(summary = "重发通知")
    @Parameters({
            @Parameter(name = "iToken", description = "用户身份凭证", required = true, in = ParameterIn.HEADER),
            @Parameter(name = "notifyId", description = "商户通知记录ID", required = true)
    })
    @PreAuthorize("hasAuthority('ENT_MCH_NOTIFY_RESEND')")
    @PostMapping("/resend/{notifyId}")
    public ApiRes<MchNotifyRecord> resend(@PathVariable("notifyId") Long notifyId) {
        MchNotifyRecord mchNotify = mchNotifyService.getById(notifyId);
        if (mchNotify == null) {
            return ApiRes.fail(ApiCodeEnum.SYS_OPERATION_FAIL_SELETE);
        }
        
        // 检查是否有权限操作该通知记录（记录必须属于代理商关联的商户）
        String currentAgentNo = getCurrentUser().getSysUser().getBelongInfoId();
        List<String> mchNos = agentMchRelationService.getMchNosByAgentNo(currentAgentNo);
        
        if (!mchNos.contains(mchNotify.getMchNo())) {
            return ApiRes.fail(ApiCodeEnum.SYS_PERMISSION_ERROR);
        }
        
        if (mchNotify.getState() != MchNotifyRecord.STATE_FAIL) {
            throw new BizException("请选择失败的通知记录");
        }

        //更新通知中
        mchNotifyService.getBaseMapper().updateIngAndAddNotifyCountLimit(notifyId);

        //调起MQ重发
        mqSender.send(PayOrderMchNotifyMQ.build(notifyId));

        return ApiRes.ok(mchNotify);
    }

}
