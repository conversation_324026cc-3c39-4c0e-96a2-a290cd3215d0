// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/vue-next/pull/3399

declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('ant-design-vue/es')['Alert']
    AAvatar: typeof import('ant-design-vue/es')['Avatar']
    ABadge: typeof import('ant-design-vue/es')['Badge']
    ABreadcrumb: typeof import('ant-design-vue/es')['Breadcrumb']
    ABreadcrumbItem: typeof import('ant-design-vue/es')['BreadcrumbItem']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACheckboxGroup: typeof import('ant-design-vue/es')['CheckboxGroup']
    ACol: typeof import('ant-design-vue/es')['Col']
    AConfigProvider: typeof import('ant-design-vue/es')['ConfigProvider']
    ADescriptions: typeof import('ant-design-vue/es')['Descriptions']
    ADescriptionsItem: typeof import('ant-design-vue/es')['DescriptionsItem']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADrawer: typeof import('ant-design-vue/es')['Drawer']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ALayoutSider: typeof import('ant-design-vue/es')['LayoutSider']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    ARangePicker: typeof import('ant-design-vue/es')['RangePicker']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASkeleton: typeof import('ant-design-vue/es')['Skeleton']
    ASpace: typeof import('ant-design-vue/es')['Space']
    AStatistic: typeof import('ant-design-vue/es')['Statistic']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATree: typeof import('ant-design-vue/es')['Tree']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    AvatarDropdown: typeof import('./src/components/GlobalHeader/AvatarDropdown.vue')['default']
    ChannelUserModal: typeof import('./src/components/ChannelUser/ChannelUserModal.vue')['default']
    GlobalFooter: typeof import('./src/components/GlobalFooter/index.vue')['default']
    GlobalLoad: typeof import('./src/components/GlobalLoad/GlobalLoad.vue')['default']
    JeepayCard: typeof import('./src/components/JeepayCard/JeepayCard.vue')['default']
    JeepayDrChildren: typeof import('./src/components/JeepayTable/JeepayDrChildren.vue')['default']
    JeepayLayout: typeof import('./src/components/JeepayLayout/JeepayLayout.vue')['default']
    JeepayMenu: typeof import('./src/components/JeepayTable/JeepayMenu.vue')['default']
    JeepayTable: typeof import('./src/components/JeepayTable/JeepayTable.vue')['default']
    JeepayTableColState: typeof import('./src/components/JeepayTable/JeepayTableColState.vue')['default']
    JeepayTableColumns: typeof import('./src/components/JeepayTable/JeepayTableColumns.vue')['default']
    JeepayTextUp: typeof import('./src/components/JeepayTextUp/JeepayTextUp.vue')['default']
    JeepayUpload: typeof import('./src/components/JeepayUpload/JeepayUpload.vue')['default']
    RightContent: typeof import('./src/components/GlobalHeader/RightContent.vue')['default']
    SubMenu: typeof import('./src/components/JeepayLayout/SubMenu.vue')['default']
  }
}

export { }
