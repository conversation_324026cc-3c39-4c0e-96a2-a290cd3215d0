@import 'ant-design-vue/dist/reset.css';

// 清除头部栏下方阴影
.ant-layout-header,
.ant-pro-basicLayout .ant-layout-header:not(.ant-pro-top-menu) {
  background: initial;
}
// 清除头部栏下方阴影 修改背景色
.ant-pro-global-header {
  background: initial;
  box-shadow: initial;
}
// 面包屑导航部分 修改背景色
.ant-pro-page-header-wrap-page-header-warp {
  background: initial;
}

//左上角 logo 图标样式
.ant-pro-sider-menu-logo {
  padding-left: 30px;
}
.ant-pro-sider-menu-logo svg {
  height: 26px;
  width: initial;
}

.ant-pro-global-header-index-right {
  margin-right: 8px;

  &.ant-pro-global-header-index-dark {
    .ant-pro-global-header-index-action {
      color: hsla(0, 0%, 100%, 0.85);

      &:hover {
        background: #1890ff;
      }
    }
  }

  .ant-pro-account-avatar {
    .antd-pro-global-header-index-avatar {
      // margin: ~'calc((@{layout-header-height} - 24px) / 2)' 0;
      margin-right: 8px;
      color: @primary-color;
      vertical-align: top;
      background: rgba(255, 255, 255, 0.85);
    }
  }

  .menu {
    .anticon {
      margin-right: 8px;
    }

    .ant-dropdown-menu-item {
      min-width: 100px;
    }
  }
}
