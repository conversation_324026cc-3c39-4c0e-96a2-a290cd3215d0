<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">
  <g id="聚合主扫" transform="translate(-6475 -11607)">
    <path id="路径_4159" data-name="路径 4159" d="M190.234,137.3l-.02-.02-.02-.02c-1.443-1.443-3.6-2.177-6.514-2.562a91.718,91.718,0,0,0-10.431-.453,91.681,91.681,0,0,0-10.431.453c-2.93.387-5.091,1.126-6.534,2.583s-2.2,3.6-2.582,6.534a91.7,91.7,0,0,0-.453,10.431,91.682,91.682,0,0,0,.453,10.431c.385,2.917,1.12,5.071,2.563,6.514l.02.02.02.02c1.443,1.443,3.6,2.177,6.514,2.562a91.714,91.714,0,0,0,10.431.453,91.681,91.681,0,0,0,10.431-.453c2.93-.387,5.091-1.126,6.534-2.583s2.2-3.6,2.582-6.534a91.729,91.729,0,0,0,.453-10.431,91.681,91.681,0,0,0-.453-10.431c-.385-2.917-1.12-5.071-2.563-6.514Z" transform="translate(6321.75 11472.75)" fill="#354268"/>
    <rect id="矩形_2549" data-name="矩形 2549" width="22" height="1.5" transform="translate(6484 11626.25)" fill="#fff"/>
    <path id="联合_40" data-name="联合 40" d="M0,8V1A1,1,0,0,1,1,0H8V1.5H1.5V8Z" transform="translate(6485 11617)" fill="#fff"/>
    <path id="联合_41" data-name="联合 41" d="M0,8V1A1,1,0,0,1,1,0H8V1.5H1.5V8Z" transform="translate(6505 11617) rotate(90)" fill="#fff"/>
    <g id="组_1001" data-name="组 1001" transform="translate(0 17)">
      <path id="联合_43" data-name="联合 43" d="M0,8V1A1,1,0,0,1,1,0H8V1.5H1.5V8Z" transform="translate(6505 11620) rotate(180)" fill="#fff"/>
      <path id="联合_42" data-name="联合 42" d="M0,8V1A1,1,0,0,1,1,0H8V1.5H1.5V8Z" transform="translate(6485 11620) rotate(-90)" fill="#fff"/>
    </g>
    <path id="矩形_2559" data-name="矩形 2559" d="M1,0H11a1,1,0,0,1,1,1V4a0,0,0,0,1,0,0H0A0,0,0,0,1,0,4V1A1,1,0,0,1,1,0Z" transform="translate(6489 11621)" fill="#fff"/>
    <path id="矩形_2560" data-name="矩形 2560" d="M0,0H12a0,0,0,0,1,0,0V3a1,1,0,0,1-1,1H1A1,1,0,0,1,0,3V0A0,0,0,0,1,0,0Z" transform="translate(6489 11629)" fill="#fff"/>
  </g>
</svg>
