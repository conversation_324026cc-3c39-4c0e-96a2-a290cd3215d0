<template>
  <a-drawer
    title="代理商详情"
    :width="800"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    @close="onClose"
  >
    <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-row>
        <a-col :span="12">
          <a-form-item label="代理商号">
            <a-input :value="detailData.agentNo" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="代理商名称">
            <a-input :value="detailData.agentName" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="代理商简称">
            <a-input :value="detailData.agentShortName" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="代理商层级">
            <a-tag :color="getAgentLevelColor(detailData.agentLevel)">
              {{ detailData.agentLevel }}级代理商
            </a-tag>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="上级代理商">
            <a-input :value="parentAgentName || detailData.parentAgentNo || '无'" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="代理商状态">
            <a-tag :color="detailData.state === 1 ? 'green' : 'red'">
              {{ detailData.state === 1 ? '正常' : '停用' }}
            </a-tag>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="联系人姓名">
            <a-input :value="detailData.contactName" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="联系人手机">
            <a-input :value="detailData.contactTel" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="联系人邮箱">
            <a-input :value="detailData.contactEmail" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="分润比例">
            <a-input :value="detailData.profitRate ? (detailData.profitRate * 100).toFixed(2) + '%' : '0%'" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="省份">
            <a-input :value="detailData.province" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="城市">
            <a-input :value="detailData.city" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="区县">
            <a-input :value="detailData.district" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="详细地址">
            <a-input :value="detailData.address" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="创建时间">
            <a-input :value="$filters.dateFormat(detailData.createdAt)" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="更新时间">
            <a-input :value="$filters.dateFormat(detailData.updatedAt)" :disabled="true" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注信息">
            <a-textarea
              :value="detailData.remark"
              :auto-size="{ minRows: 2, maxRows: 6 }"
              :disabled="true"
              placeholder="暂无备注"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <div class="drawer-btn-center">
      <a-button :style="{ marginRight: '8px' }" style="margin-right: 8px" @click="onClose">
        关闭
      </a-button>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { API_URL_AGENT_LIST, req } from '@/api/manage'
import { reactive, getCurrentInstance } from 'vue'

const { $infoBox } = getCurrentInstance()!.appContext.config.globalProperties

const props: any = defineProps({
  callbackFunc: { type: Function },
})

const vdata: any = reactive({
  open: false, // 是否显示弹层/抽屉
  detailData: {}, // 详情数据
  parentAgentName: '', // 上级代理商名称
})

function show(recordId) {
  // 弹层打开事件
  vdata.detailData = {}
  vdata.parentAgentName = ''
  
  req.getById(API_URL_AGENT_LIST, recordId).then((res) => {
    vdata.detailData = res
    
    // 如果有上级代理商，获取上级代理商名称
    if (res.parentAgentNo) {
      req.getById(API_URL_AGENT_LIST, res.parentAgentNo).then((parentRes) => {
        vdata.parentAgentName = parentRes.agentName
      }).catch(() => {
        vdata.parentAgentName = '未知'
      })
    }
    
    vdata.open = true
  }).catch(() => {
    $infoBox.message.error('获取代理商详情失败')
  })
}

function onClose() {
  vdata.open = false
}

// 获取代理商层级颜色
function getAgentLevelColor(level) {
  const colors = {
    1: 'blue',
    2: 'green', 
    3: 'orange'
  }
  return colors[level] || 'default'
}

defineExpose({
  show
})
</script>

<style scoped>
.drawer-btn-center {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e9e9e9;
  padding: 10px 16px;
  background: #fff;
  text-align: right;
  z-index: 1;
}
</style>
