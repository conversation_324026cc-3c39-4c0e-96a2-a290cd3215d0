<template>
  <div class="empty">
    <img src="@/assets/svg/empty.svg" alt="" style="width: 100px" />
    <p style="padding-right: 5px">暂无数据</p>
  </div>
</template>

<script setup lang="ts"></script>

<!-- <style scoped lang="less">
.empty {
  width: 100%;
  height: 300px; // 与环状图一个高度
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
}
</style> -->
<style scoped lang="less">
  @empty-height: 300px; // 定义变量，方便统一修改
  
  .empty {
    width: 100%;
    min-height: @empty-height;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
    padding: 20px;
    
    // 空状态图标样式
    &-icon {
      width: 80px;
      height: 80px;
      margin-bottom: 16px;
      opacity: 0.6;
      transition: opacity 0.3s;
      
      &:hover {
        opacity: 1;
      }
    }
    
    // 空状态文本样式
    &-text {
      color: #999;
      font-size: 14px;
    }
  }
</style>